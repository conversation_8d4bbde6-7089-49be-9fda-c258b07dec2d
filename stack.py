import requests
import execjs

url = "https://webapi.cninfo.com.cn/#/aiInfos"

Enkey = execjs.compile(open("代码.js", "r", encoding="utf-8").read()).call("getResCode")

headers = {
  accept
*/*
accept-enckey
VQl23RxPmoFmqkRZSZNHlA==
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
cache-control
no-cache
connection
keep-alive
content-length
29
content-type
application/x-www-form-urlencoded; charset=UTF-8
cookie
routeId=.uc1; Hm_lvt_489bd07e99fbfc5f12cbb4145adb0a9b=**********; HMACCOUNT=221BD0C7D3C62589; MALLSSID=444C556D78657A336265627441635839702B77663156446D4B597551676C69574A624E45797466384D694D6449417578795833366B3674436B4D79694B794973; Hm_lpvt_489bd07e99fbfc5f12cbb4145adb0a9b=**********
host
webapi.cninfo.com.cn
origin
https://webapi.cninfo.com.cn
pragma
no-cache
referer
https://webapi.cninfo.com.cn/
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest
}